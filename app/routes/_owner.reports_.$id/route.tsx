import type { TestReport } from '~/gql/graphql'
import type { DeepPartial } from '~/lib/types/deep-partial'
import { pdf } from '@react-pdf/renderer'
import { format } from 'date-fns'
import { useState } from 'react'
import { useParams } from 'react-router'
import { toast } from 'sonner'
import BackButton from '~/components/common/back-button'
import CommonError from '~/components/common/common-error'
import PageHeader from '~/components/common/page-header'
import ReportPDFDocument from '~/components/common/report-pdf'
import SendTestReportDialog from '~/components/common/send-test-report-dialog'
import LoaderIcon from '~/components/icons/loader-icon'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Checkbox } from '~/components/ui/checkbox'
import { Label } from '~/components/ui/label'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetPdfSettings from '~/hooks/use-get-pdf-settings'
import useGetReportById from '~/hooks/use-get-report-by-id'
import { fetchPdfImages } from '~/lib/fetch-pdf-images'
import { cn } from '~/lib/utils'

export default function ReportById() {
  const { id } = useParams()
  const [hideEmptyFields, setHideEmptyFields] = useState(false)
  const { open: openSendReportDialog, toggleDialog: toggleSendReportDialog }
    = useDialogStates()
  const [downloadingReportId, setDownloadingReportId] = useState<number | null>(
    null,
  ) // Added state for download loading

  const { data, isLoading, isError } = useGetReportById({ id: Number(id) })
  const {
    data: pdfSettings,
    isLoading: isLoadingPdfSettings,
    isError: isErrorPdfSettings,
  } = useGetPdfSettings()

  const handleSendTestReport = async () => {
    toggleSendReportDialog(true)
  }

  const handlePrintReport = async (report: DeepPartial<TestReport>) => {
    try {
      // Fetch all required images
      const imageData = await fetchPdfImages(pdfSettings?.getPdfSetting)

      const blob = await pdf(
        <ReportPDFDocument
          reportData={report}
          pdfSettings={pdfSettings?.getPdfSetting}
          hideEmptyFields={hideEmptyFields}
          imageData={imageData}
        />,
      ).toBlob()
      const myWindow = window.open(URL.createObjectURL(blob), '_blank')
      if (myWindow) {
        myWindow.open()
        myWindow.focus()
        myWindow.print()
        // Use afterprint event to close the window after printing or cancelling
        myWindow.addEventListener('afterprint', () => {
          myWindow.close()
          URL.revokeObjectURL(myWindow.location.href) // Revoke the object URL
        })
      }
    }
    catch (error) {
      console.error('Error generating PDF for print:', error)
      toast.error('Failed to generate PDF for printing.')
    }
  }

  const handleDownloadReport = async (report: DeepPartial<TestReport>) => {
    if (report)
      setDownloadingReportId(report.id!)
    try {
      // Fetch all required images
      const imageData = await fetchPdfImages(pdfSettings?.getPdfSetting)

      const blob = await pdf(
        <ReportPDFDocument
          reportData={report}
          pdfSettings={pdfSettings?.getPdfSetting}
          hideEmptyFields={hideEmptyFields}
          imageData={imageData}
        />,
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `report-${report.id}.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
    catch (error) {
      console.error('Error generating or downloading PDF:', error)
      toast.error('Failed to download report.')
    }
    finally {
      setDownloadingReportId(null)
    }
  }

  if (isLoading || isLoadingPdfSettings) {
    return (
      <div className="flex justify-center items-center grow">
        <LoaderIcon className="size-16 animate-spin" />
      </div>
    )
  }

  if (isError || !data?.getReportById?.template || isErrorPdfSettings) {
    return <CommonError />
  }

  const report = data.getReportById

  return (
    <>
      <div
        className="flex flex-col grow max-w-4xl mx-auto w-full"
        data-testid="report-content"
      >
        <div className="flex justify-between items-center">
          <div className="back-button">
            <BackButton />
          </div>
          <div className="flex gap-x-6">
            <Button onClick={() => handlePrintReport(report)}>
              Print Report
            </Button>
            <Button onClick={handleSendTestReport}>Send Report</Button>
            <Button
              onClick={() => handleDownloadReport(report)}
              isLoading={downloadingReportId === report.id}
            >
              Download Report
            </Button>
          </div>
        </div>
        <div className="flex justify-end mt-4">
          <Label className="bg-black text-white px-4 py-2 rounded-md">
            <Checkbox
              checked={hideEmptyFields}
              className="bg-white data-[state=checked]:bg-green-500 data-[state=checked]:text-black"
              onCheckedChange={(e) => {
                if (e) {
                  setHideEmptyFields(true)
                }
                else {
                  setHideEmptyFields(false)
                }
              }}
            />
            Hide empty fields in PDF
          </Label>
        </div>

        {/* Patient Information */}
        <div className="grid grid-cols-2 py-4">
          <div className="col-span-1 flex flex-col gap-2">
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Name</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.patient.name}</div>
            </div>
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Age</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.patient.age}</div>
            </div>
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Gender</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.patient.gender}</div>
            </div>
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Phone</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.patient.phone_number}</div>
            </div>
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Address</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.patient.address}</div>
            </div>
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Blood Group</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.patient.blood_group}</div>
            </div>
            <div className="flex">
              <div className="basis-1/2 justify-between flex pr-4">
                <span>Ref Doctor</span>
                <span>:</span>
              </div>
              <div className="basis-1/2">{report.doctor?.name}</div>
            </div>
          </div>
          <div className="col-span-1 flex flex-col gap-2">
            <div className="flex">
              <div className="basis-1/2 ">
                <span>Collection Date</span>
              </div>
              <div className="basis-1/2 text-right">
                <span>:</span>
                <span className="pl-4">
                  {report.collection_date
                    ? format(report.collection_date, 'dd-MMM-yyyy')
                    : ''}
                </span>
              </div>
            </div>
            <div className="flex">
              <div className="basis-1/2 ">
                <span>Test Date</span>
              </div>
              <div className="basis-1/2 text-right">
                <span>:</span>
                <span className="pl-4">
                  {report.test_date
                    ? format(report.test_date, 'dd-MMM-yyyy')
                    : ''}
                </span>
              </div>
            </div>
            <div className="flex">
              <div className="basis-1/2 ">
                <span>Report generation date</span>
              </div>
              <div className="basis-1/2 text-right">
                <span>:</span>
                <span className="pl-4">
                  {report.report_generation_date
                    ? format(report.report_generation_date, 'dd-MMM-yyyy')
                    : ''}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-center py-4 border-t ">
          <PageHeader title={report.template.name} />
        </div>

        {/* Report Sections */}
        <div className="flex flex-col gap-y-8">
          {report.template_data.sections?.map((section, index) => {
            return (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="sr-only">{section.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-12 gap-4 font-semibold border-b pb-2">
                    <div className="col-span-4">{section.name}</div>
                    <div className="col-span-3">Result</div>
                    {section.fields?.some(field => field.unit_active) && (
                      <div className="col-span-2">Unit</div>
                    )}
                    {section.fields?.some(
                      field => field.reference_active,
                    ) && <div className="col-span-3">BIO.REF.INTERVAL</div>}
                  </div>
                  <div className="flex flex-col gap-2 mt-2">
                    {section.fields?.map((field, fieldIndex) => (
                      <div key={fieldIndex}>
                        {field.input_type === 'subfield'
                          ? (
                              <div>
                                <div className="font-medium mb-2">{field.name}</div>
                                {field.sub_fields?.map(
                                  (subField, subFieldIndex: number) => (
                                    <div
                                      key={subFieldIndex}
                                      className="grid grid-cols-12 gap-4 py-1"
                                    >
                                      <div className="col-span-4 pl-8">
                                        {subField.name}
                                      </div>
                                      <div className="col-span-3">
                                        {subField.name_value}
                                      </div>
                                      <div className="col-span-2">
                                        {subField.unit_value}
                                      </div>
                                      <div className="col-span-3 whitespace-normal">
                                        {subField.reference_value}
                                      </div>
                                    </div>
                                  ),
                                )}
                              </div>
                            )
                          : (
                              <div className="grid grid-cols-12 gap-4 py-1">
                                <div className="col-span-4">{field.name}</div>
                                {field.input_type === 'richtext'
                                  ? (
                                      <div
                                        className={cn(
                                          'col-span-3 prose whitespace-normal',
                                          {
                                            'col-span-8':
                                      !field.unit_active
                                      && !field.reference_active,
                                          },
                                        )}
                                        dangerouslySetInnerHTML={{
                                          __html: field.name_value ?? '',
                                        }}
                                      />
                                    )
                                  : (
                                      <div className="col-span-3">
                                        {field.input_type === 'multiselect'
                                          ? field.input_type_values?.join(', ')
                                          : field.name_value}
                                      </div>
                                    )}
                                {field.unit_active
                                  ? (
                                      <div className="col-span-2">
                                        {field.unit_value}
                                      </div>
                                    )
                                  : <div className="col-span-2" />}
                                {field.reference_active
                                  ? (
                                      <div className="col-span-3 whitespace-pre-wrap">
                                        {field.reference_value}
                                      </div>
                                    )
                                  : <div className="col-span-2" />}
                              </div>
                            )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
        <SendTestReportDialog
          open={openSendReportDialog}
          onOpenChange={() => {
            toggleSendReportDialog(false)
          }}
          report={data?.getReportById as DeepPartial<TestReport>}
          pdfSettings={pdfSettings?.getPdfSetting}
          hideEmptyFields={hideEmptyFields}
        />
      </div>
    </>
  )
}
