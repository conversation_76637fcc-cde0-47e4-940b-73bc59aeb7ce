import type { UpdateTemplateType } from './schema'
import { HexColorPicker } from 'react-colorful'
import CustomTooltip from '~/components/common/custom-tooltip'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import { Label } from '~/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { withForm } from '~/hooks/form'

export const UpdateFormColors = withForm({
  defaultValues: {
    title_color: '',
    report_name_color: '',
  } as UpdateTemplateType,
  render: ({ form }) => {
    return (
      <div className="flex gap-x-4">
        <form.Field
          name="title_color"
          children={({ state, handleChange }) => (
            <FormItem className="flex flex-row gap-x-4 basis-1/2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="title_color"
                    type="button"
                    style={{ backgroundColor: state.value || '#000' }}
                    size="icon"
                  />
                </PopoverTrigger>
                <PopoverContent>
                  <HexColorPicker
                    defaultValue="#000"
                    color={state.value || '#000'}
                    onChange={handleChange}
                  />
                </PopoverContent>
              </Popover>
              <Label htmlFor="title_color">Title color</Label>
            </FormItem>
          )}
        />
        <form.Field
          name="report_name_color"
          children={({ state, handleChange }) => (
            <FormItem className="flex flex-row gap-x-4 basis-1/2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="report_name_color"
                    type="button"
                    style={{ backgroundColor: state.value || '#000' }}
                    size="icon"
                  />
                </PopoverTrigger>
                <PopoverContent>
                  <HexColorPicker
                    defaultValue="#000"
                    color={state.value || '#000'}
                    onChange={handleChange}
                  />
                </PopoverContent>
              </Popover>
              <Label htmlFor="report_name_color">Report name color</Label>
            </FormItem>
          )}
        />
      </div>
    )
  },
})
