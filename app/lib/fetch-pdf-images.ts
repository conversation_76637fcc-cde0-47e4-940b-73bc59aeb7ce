import type { GetPdfSettingQuery } from '~/gql/graphql'
import { fetchImageAsDataUrl } from './fetch-image-as-data-url'

export interface PdfImageData {
  logo?: string
  leftSignature?: string
  rightSignature?: string
}

export async function fetchPdfImages(
  pdfSettings: GetPdfSettingQuery['getPdfSetting'] | null | undefined,
): Promise<PdfImageData> {
  if (!pdfSettings) {
    return {}
  }

  try {
    const imagePromises: Promise<[string, string]>[] = []

    // Collect all image paths that need to be fetched
    if (pdfSettings.logo?.path) {
      imagePromises.push(
        fetchImageAsDataUrl(pdfSettings.logo.path).then(dataUrl => ['logo', dataUrl] as [string, string]),
      )
    }

    if (pdfSettings.leftSignature?.path) {
      imagePromises.push(
        fetchImageAsDataUrl(pdfSettings.leftSignature.path).then(dataUrl => ['leftSignature', dataUrl] as [string, string]),
      )
    }

    if (pdfSettings.rightSignature?.path) {
      imagePromises.push(
        fetchImageAsDataUrl(pdfSettings.rightSignature.path).then(dataUrl => ['rightSignature', dataUrl] as [string, string]),
      )
    }

    // Fetch all images concurrently
    const results = await Promise.all(imagePromises)

    // Convert results to object
    const imageData = results.reduce((acc, [key, dataUrl]) => {
      acc[key as keyof PdfImageData] = dataUrl
      return acc
    }, {} as PdfImageData)

    return imageData
  }
  catch (error) {
    console.error('Error loading PDF images:', error)
    return {}
  }
}
