import type { GetPdfSettingQuery } from '~/gql/graphql'
import { Document, Image, Page, PDFViewer, StyleSheet, Text, View } from '@react-pdf/renderer'
import { useCallback, useEffect, useState } from 'react'
import { fetchImageAsDataUrl } from '~/lib/fetch-image-as-data-url'

interface Props {
  pdfSettings: GetPdfSettingQuery['getPdfSetting'] | null | undefined
}

const styles = StyleSheet.create({
  document: {
    backgroundColor: '#FFFFFF',
    width: '100%',
    display: 'flex',
  },
  page: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    padding: 30,
    fontSize: 10, // Default text size
    position: 'relative', // Added for absolute positioning of footer
  },
  patientSection: { display: 'flex', flexDirection: 'row', marginBottom: 15 },
  patientInfoColumn: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    gap: 4,
  },
  patientDataRow: { flexDirection: 'row', display: 'flex' },
  patientLabelCell: {
    flexBasis: '35%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingRight: 5,
    display: 'flex',
  },
  patientValueCell: { flexBasis: '65%', flexWrap: 'wrap' },
  dateLabelCell: {
    flexBasis: '65%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingRight: 5,
  },
  dateValueCell: { flexBasis: '35%', flexWrap: 'wrap' },
  boldText: { fontWeight: 'bold' },

  reportTitle: {
    fontSize: 20,
    textAlign: 'center',
    marginVertical: 15,
    fontWeight: 'bold',
  },

  sectionCard: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    marginBottom: 32,
    padding: 8,
  }, // Changed border: 1 to borderWidth: 1

  imageBlock: {
    width: 50,
    height: 50,
    position: 'absolute',
    top: 0,
    left: 0,
  },
  headerSection: {
    display: 'flex',
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    marginBottom: 15,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },

  titleBlock: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    alignItems: 'center',
    width: '100%',
    flex: 1,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    width: '100%',
    textAlign: 'center',
  },
  subtitle: { width: '100%', textAlign: 'center', whiteSpace: 'pre-wrap' },
  reference: { whiteSpace: 'pre-wrap' },
  preWrap: { whiteSpace: 'pre-wrap' },
  footerContainer: {
    display: 'flex',
    flexDirection: 'row',
    whiteSpace: 'pre-wrap',
  },
  bottomFooterLeft: {
    textAlign: 'left',
    whiteSpace: 'pre-wrap',
    basis: '33.33%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  bottomFooterCenter: {
    whiteSpace: 'pre-wrap',
    height: '100%',
    basis: '33.33%',
    width: '100%',
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  bottomFooterRight: {
    textAlign: 'right',
    whiteSpace: 'pre-wrap',
    basis: '33.33%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  signatureImage: {
    width: 50,
    height: 50,
    marginBottom: 5,
  },
})

export default function PDFPreview({ pdfSettings }: Props) {
  const [imageData, setImageData] = useState<{
    logo?: string
    leftSignature?: string
    rightSignature?: string
  }>({})
  const [isLoading, setIsLoading] = useState(true)

  const loadImages = useCallback(async () => {
    if (!pdfSettings) {
      setIsLoading(false)
      return
    }

    setIsLoading(true)

    try {
      const imagePromises: Promise<[string, string]>[] = []

      // Collect all image paths that need to be fetched
      if (pdfSettings.logo?.path) {
        imagePromises.push(
          fetchImageAsDataUrl(pdfSettings.logo.path).then(dataUrl => ['logo', dataUrl] as [string, string]),
        )
      }

      if (pdfSettings.leftSignature?.path) {
        imagePromises.push(
          fetchImageAsDataUrl(pdfSettings.leftSignature.path).then(dataUrl => ['leftSignature', dataUrl] as [string, string]),
        )
      }

      if (pdfSettings.rightSignature?.path) {
        imagePromises.push(
          fetchImageAsDataUrl(pdfSettings.rightSignature.path).then(dataUrl => ['rightSignature', dataUrl] as [string, string]),
        )
      }

      // Fetch all images concurrently
      const results = await Promise.all(imagePromises)

      // Convert results to object
      const newImageData = results.reduce((acc, [key, dataUrl]) => {
        acc[key as keyof typeof acc] = dataUrl
        return acc
      }, {} as typeof imageData)

      setImageData(newImageData)
    }
    catch (error) {
      console.error('Error loading images:', error)
      setImageData({})
    }
    finally {
      setIsLoading(false)
    }
  }, [pdfSettings])

  useEffect(() => {
    loadImages()
  }, [loadImages])

  return (
    !isLoading && (
      <PDFViewer style={styles.document}>
        <Document style={styles.document}>
          <Page size="A4" style={styles.page}>
            <View style={styles.headerSection}>
              {imageData.logo
                ? (
                    <View style={styles.imageBlock}>
                      <Image src={imageData.logo} />
                    </View>
                  )
                : null}
              <View style={styles.titleBlock}>
                <Text style={styles.title}>{pdfSettings?.title}</Text>
                <Text style={styles.subtitle}>{pdfSettings?.subtitle}</Text>
              </View>
            </View>

            <View style={styles.patientSection}>
              <View style={styles.patientInfoColumn}>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Name</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Age</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Gender</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Phone</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Address</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Blood Group</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Ref. Doctor</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
              </View>

              <View style={styles.patientInfoColumn}>
                <View style={styles.patientDataRow}>
                  <View style={styles.dateLabelCell}>
                    <Text style={styles.boldText}>Collection Date</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.dateValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.dateLabelCell}>
                    <Text style={styles.boldText}>Test Date</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.dateValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.dateLabelCell}>
                    <Text style={styles.boldText}>Report Generation Date</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.dateValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
              </View>
            </View>
            <Text style={styles.reportTitle}>Report name</Text>
            <View style={styles.sectionCard}></View>
            <View style={styles.footerContainer}>
              <View style={styles.bottomFooterLeft}>
                {imageData.leftSignature && (
                  <Image src={imageData.leftSignature} style={styles.signatureImage} />
                )}
                <Text>{pdfSettings?.footer_left}</Text>
              </View>
              <View style={styles.bottomFooterCenter}>
                <Text>{pdfSettings?.footer_center}</Text>
              </View>
              <View style={styles.bottomFooterRight}>
                {imageData.rightSignature && (
                  <Image src={imageData.rightSignature} style={styles.signatureImage} />
                )}
                <Text>{pdfSettings?.footer_right}</Text>
              </View>
            </View>
          </Page>
        </Document>
      </PDFViewer>
    )
  )
}
