import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { GET_TEMPLATES } from '~/graphql/queries/get-templates'
import { graphqlClient } from '~/lib/graphql-client'

const FIRST = 50

export default function useGetTemplates() {
  const [name, setName] = useQueryState('name', parseAsString.withDefault(''))
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePageChange = (page: number) => {
    setPage(page)
  }

  const handleNameChange = (name: string) => {
    setName(name)
    setPage(1)
  }

  const [debouncedName] = useDebounce(name, 500)

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-templates', page, debouncedName],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_TEMPLATES,
        variables: {
          name: debouncedName,
          first: FIRST,
          page,
        },
      })
    },
  })

  const totalItems = data?.getTemplates?.paginator_info?.total || 0
  const totalPages = Math.ceil(totalItems / FIRST) || 1

  return { data, isLoading, isError, handlePageChange, page, totalPages, name, handleNameChange }
}
