import { Link } from 'react-router'
import PagePagination from '~/components/common/page-pagination'
import { Input } from '~/components/ui/input'
import { Skeleton } from '~/components/ui/skeleton'
import useGetTemplates from '~/hooks/use-get-templates'

export default function DashboardTemplates() {
  const { data, isLoading, isError, totalPages, page, handlePageChange, name, handleNameChange } = useGetTemplates()

  return (
    <>
      <div className="flex flex-col grow">
        <Input
          value={name}
          onChange={e => handleNameChange(e.target.value)}
          placeholder="Search by report name"
          className="w-128"
        />
        <div className="flex flex-wrap gap-4 mt-4">
          {isLoading && (
            <>
              {Array.from({ length: 6 }).map((_, index) => (
                <Skeleton className="size-48 bg-white" key={index} />
              ))}
            </>
          )}
          {isError && (
            <div className="text-center">Error. Unable to get templates.</div>
          )}
          {data?.getTemplates.data?.length === 0 && (
            <div className="text-center">No templates found.</div>
          )}
          {!isLoading && !isError && data?.getTemplates.data?.map((item) => {
            return (
              <Link
                to={`/create-report/${item.slug}`}
                key={item.id}
                className="flex bg-white items-center justify-center border size-48 hover:bg-input border-black rounded-md p-4 text-center rounded-md"
              >
                {item.name}
              </Link>
            )
          })}
        </div>
      </div>
      {totalPages > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePageChange}
          lastPage={totalPages}
        />
      )}
    </>
  )
}
